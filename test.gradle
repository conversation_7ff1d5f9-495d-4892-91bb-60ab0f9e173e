test {
    useJUnitPlatform()
    finalizedBy jacocoTestReport
    testLogging {
        events = ['passed', 'skipped', 'failed']
        showExceptions = true
        exceptionFormat = 'full'
        showCauses = true
        showStackTraces = true
    }
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.required = false
        csv.required = false
        html.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.6
            }
        }
    }
}

dependencyManagement {
    imports {
        mavenBom "org.testcontainers:testcontainers-bom:${testcontainersVersion}"
    }
}

dependencies {
    testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'

    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'

    testImplementation "org.wiremock:wiremock-standalone:${wiremockVersion}"
    testImplementation "org.wiremock.integrations.testcontainers:wiremock-testcontainers-module:${wiremockTestcontainersVersion}"

    testCompileOnly("org.projectlombok:lombok")
    testAnnotationProcessor("org.projectlombok:lombok")
}

