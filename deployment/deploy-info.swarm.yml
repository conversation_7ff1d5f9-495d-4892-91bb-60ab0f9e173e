exchange-rate-service:
  version: "${GO_PIPELINE_LABEL}"
  image: "${EXCHANGE_RATE_SERVICE_DOCKER_IMAGE}"
  dns_name: exchange-rate-service
  port: 3000
  replicas: ${EXCHANGE_RATE_SERVICE_REPLICAS}
  memory_limit: "${EXCHANGE_RATE_SERVICE_MEMORY_LIMIT}"
  environment_variables:
    - _JAVA_OPTIONS=${EXCHANGE_RATE_SERVICE_ENV_JAVA_OPTIONS}
    - DATABASE_HOST=${EXCHANGE_RATE_SERVICE_ENV_DATABASE_HOST}
    - DATABASE_NAME=${EXCHANGE_RATE_SERVICE_ENV_DATABASE_NAME}
    - DATABASE_PORT=${EXCHANGE_RATE_SERVICE_ENV_DATABASE_PORT}
    - DATABASE_USER=${EXCHANGE_RATE_SERVICE_ENV_DATABASE_USER}
    - DATADOG_API_KEY=${EXCHANGE_RATE_SERVICE_ENV_DATADOG_API_KEY}
    - DATADOG_ENABLED=${EXCHANGE_RATE_SERVICE_ENV_DATADOG_ENABLED}
    - METRICS_ENV=${EXCHANGE_RATE_SERVICE_ENV_METRICS_ENV}
    - OPEN_EXCHANGE_RATES_URL=${EXCHANGE_RATE_SERVICE_ENV_OPEN_EXCHANGE_RATES_URL}
    - OPEN_EXCHANGE_RATES_KEY=${EXCHANGE_RATE_SERVICE_ENV_OPEN_EXCHANGE_RATES_KEY}
