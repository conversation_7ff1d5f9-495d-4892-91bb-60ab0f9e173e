server.port: 3000

management:
  datadog:
    metrics:
      export:
        enabled: ${DATADOG_ENABLED:false}
        host-tag: host
        api-key: ${DATADOG_API_KEY:dummy-key}
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,loggers
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
    metrics:
      enabled: true
  metrics:
    enable:
      all: false
      spring.component.calls: true
      http: true
      executor.execution: true
    distribution:
      percentiles-histogram:
        all: false
        http: true
      percentiles:
        all: 0.99, 0.95
  health:
    readinessstate:
      enabled: true
    livenessstate:
      enabled: true
  tracing:
    sampling:
      probability: 1
    propagation:
      consume: w3c
      produce: w3c
    baggage:
      remote-fields: traceId, sessionId, customerId
      correlation:
        fields:
          - traceId
          - sessionId
          - customerId

spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: exchange-rate-service
  profiles:
    active: aws
  sql:
    init:
      platform: postgres
  datasource:
    url: jdbc:postgresql://${DATABASE_HOST:postgres}:${DATABASE_PORT:5432}/${DATABASE_NAME:exchange_rates_db}
    username: ${DATABASE_USER:exchangerates}
    password: ${DATABASE_PASSWORD:exchangerates}
    hikari:
      maximum-pool-size: 20
  jpa:
    open-in-view: false
    show-sql: false
  codec:
    log-request-details: true

logging:
  include-application-name: false
  level:
    "ch.qos.logback.classic": warn
    "org.hibernate.engine.jdbc.spi.SqlExceptionHelper": error
    org.springframework.web.reactive.function.client.ExchangeFunctions: trace

springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

open-exchange-client:
  url: ${OPEN_EXCHANGE_RATES_URL:https://openexchangerates.org}
  api-key: ${OPEN_EXCHANGE_RATES_KEY:}
