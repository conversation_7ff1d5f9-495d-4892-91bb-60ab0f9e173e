-- To change rate we must first drop any related views
drop view if exists latest_exchange_rates;

-- Maximum precision for Presto
alter table exchange_rates alter column rate type decimal(38, 19);

-- Rebuild view (Unchanged)
create or replace view latest_exchange_rates as
with ranked_exchange_rates as (
    SELECT *, rank() over (partition by from_currency,to_currency order by created_at desc) rnk
    FROM exchange_rates
    WHERE created_at >= now() at time zone 'utc' - interval '1' hour
)
select id, from_currency, to_currency, rate, created_at
from ranked_exchange_rates
where rnk = 1;