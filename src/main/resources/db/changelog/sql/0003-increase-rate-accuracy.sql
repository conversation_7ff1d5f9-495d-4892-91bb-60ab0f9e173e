drop view if exists latest_exchange_rates;

alter table exchange_rates alter column rate type decimal;

create or replace view latest_exchange_rates as
with ranked_exchange_rates as (
    SELECT *, rank() over (partition by from_currency,to_currency order by created_at desc) rnk
    FROM exchange_rates
    WHERE created_at >= now() at time zone 'utc' - interval '1' hour
)
select id, from_currency, to_currency, rate, created_at
from ranked_exchange_rates
where rnk = 1;