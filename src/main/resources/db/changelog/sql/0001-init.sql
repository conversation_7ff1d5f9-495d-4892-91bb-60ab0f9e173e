create table if not exists exchange_rates
(
    id              serial primary key,
    from_currency   varchar not null,
    to_currency     varchar not null,
    rate            numeric(11, 4),
    created_at      timestamp
);

create index if not exists exchange_rates_from_idx on exchange_rates(from_currency asc);
create index if not exists exchange_rates_timestamp_idx on exchange_rates(created_at desc);

create or replace view latest_exchange_rates as
with ranked_exchange_rates as (
    SELECT *, rank() over (partition by from_currency,to_currency order by created_at desc) rnk
    FROM exchange_rates
)
select id, from_currency, to_currency, rate, created_at
from ranked_exchange_rates
where rnk = 1;