package com.propero.exchangerates.clients;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

@HttpExchange(url = "/api", accept = MediaType.APPLICATION_JSON_VALUE)
public interface OpenExchangeClient {

    @GetExchange("latest.json")
    Response getLatest(
            @RequestParam(required = false) @Nullable String base,
            @RequestParam(required = false) @Nullable String symbols
    );

    @GetExchange("historical/{date}.json")
    Response getHistorical(
            @PathVariable("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) @Nullable String base,
            @RequestParam(required = false) @Nullable String symbols
    );

    record Response(String disclaimer, String license, Long timestamp, String base, Map<String, BigDecimal> rates){}
}
