package com.propero.exchangerates.rest;

import com.propero.exchangerates.providers.ExchangeRateProvider;
import com.propero.exchangerates.providers.ExchangeRateProvider.ExchangeRates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ExchangeRateService {

    private final ExchangeRateProvider databaseExchangeRatesProvider;
    private final ExchangeRateProvider openExchangeRatesProvider;

    public Optional<ExchangeRates> getLatestExchangeRates(String baseCurrency, List<String> symbols) {
        return databaseExchangeRatesProvider.getLatest(baseCurrency, symbols)
                .or(() -> openExchangeRatesProvider.getLatest(baseCurrency, symbols)
                        .stream()
                        .peek(databaseExchangeRatesProvider::save)
                        .findFirst());
    }

    public Optional<ExchangeRates> getExchangeRates(LocalDate date, String baseCurrency, List<String> symbols) {
        return databaseExchangeRatesProvider.get(date, baseCurrency, symbols)
                .or(() -> openExchangeRatesProvider.get(date, baseCurrency, symbols)
                        .stream()
                        .peek(databaseExchangeRatesProvider::save)
                        .findFirst());
    }

    public void refresh(String baseCurrency) {
        openExchangeRatesProvider.getLatest(baseCurrency, List.of()).ifPresent(databaseExchangeRatesProvider::save);
    }
}
