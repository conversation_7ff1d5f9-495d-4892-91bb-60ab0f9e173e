package com.propero.exchangerates.rest;

import com.propero.exchangerates.providers.ExchangeRateProvider.ExchangeRates;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class ExchangeRateController {

    private final ExchangeRateService exchangeRateService;

    @GetMapping("/latest")
    public ResponseEntity<ExchangeRates> fetchLatestExchangeRate(
        @RequestParam(value = "base", defaultValue = "USD", required = false) String baseCurrency,
        @RequestParam(value = "symbols", defaultValue = "", required = false) List<String> symbols
    ) {
        log.info("Fetching latest exchange rates for base: {}, symbols: {}", baseCurrency, symbols);
        return exchangeRateService.getLatestExchangeRates(baseCurrency, symbols)
                .map(ResponseEntity::ok)
                .orElseGet(() -> {
                    log.warn("Exchange rate for {} not found", baseCurrency);
                    return ResponseEntity.noContent().build();
                });
    }

    @GetMapping("/historical/{date}")
    public ResponseEntity<ExchangeRates> fetchHistoricalExchangeRate(
            @PathVariable(name = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)  LocalDate date,
            @RequestParam(name = "base", defaultValue = "USD", required = false) String baseCurrency,
            @RequestParam(name = "symbols", defaultValue = "", required = false) List<String> symbols
    ) {
        return exchangeRateService.getExchangeRates(date, baseCurrency, symbols)
                .map(ResponseEntity::ok)
                .orElseGet(() -> {
                    log.warn("Exchange rate for {} at {} not found", baseCurrency, date);
                    return ResponseEntity.noContent().build();
                });
    }

    @GetMapping("/refresh")
    @ResponseStatus(HttpStatus.OK)
    public void fetchFromRemoteSource() {
        exchangeRateService.refresh("EUR");
        exchangeRateService.refresh("USD");
    }

}
