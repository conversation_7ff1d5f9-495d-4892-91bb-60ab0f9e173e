package com.propero.exchangerates.providers;

import com.propero.exchangerates.clients.OpenExchangeClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class OpenExchangeRatesProvider implements ExchangeRateProvider {

    private final OpenExchangeClient openExchangeClient;

    @Override
    public Optional<ExchangeRates> getLatest(String base, List<String> symbols) {
        try {
            log.info("Fetching exchange rates for {} from openexchangerates.org", base);
            OpenExchangeClient.Response response = openExchangeClient.getLatest(base, toList(symbols));
            ExchangeRates exchangeRates = new ExchangeRates(response.timestamp(), response.base(), response.rates());
            return Optional.of(exchangeRates);
        } catch (Exception e) {
            log.error("Failed to fetch exchange rates for {} from openexchangerates.org", base, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<ExchangeRates> get(LocalDate date, String base, List<String> symbols) {
        try {
            log.info("Fetching exchange rates for {} at {} from openexchangerates.org", base, date);
            OpenExchangeClient.Response response = openExchangeClient.getHistorical(date, base, toList(symbols));
            ExchangeRates exchangeRates = new ExchangeRates(response.timestamp(), response.base(), response.rates());
            return Optional.of(exchangeRates);
        } catch (Exception e) {
            log.error("Failed to fetch exchange rates for {} at {} from openexchangerates.org", base, date, e);
            return Optional.empty();
        }
    }

    private static String toList(Collection<String> items) {
        return items == null ? null : String.join(",", items);
    }
}
