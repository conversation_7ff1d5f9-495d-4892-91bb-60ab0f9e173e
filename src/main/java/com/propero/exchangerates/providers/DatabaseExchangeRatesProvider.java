package com.propero.exchangerates.providers;

import com.propero.exchangerates.database.ExchangeRate;
import com.propero.exchangerates.database.ExchangeRateRepository;
import com.propero.exchangerates.database.LatestExchangeRate;
import com.propero.exchangerates.database.LatestExchangeRateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.time.Instant.ofEpochMilli;
import static java.time.Instant.ofEpochSecond;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseExchangeRatesProvider implements ExchangeRateProvider {

    private final ExchangeRateRepository exchangeRateRepository;
    private final LatestExchangeRateRepository latestExchangeRateRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional<ExchangeRates> getLatest(String base, List<String> symbols) {
        log.info("Fetching latest exchange rates for {}", base);

        List<LatestExchangeRate> rates = symbols.isEmpty() ?
                latestExchangeRateRepository.findByFromCurrency(base) :
                latestExchangeRateRepository.findByFromCurrencyAndToCurrencyIn(base, symbols);

        if (rates.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(ExchangeRates.builder()
                .base(base)
                .timestamp(rates.get(0).getCreatedAt().toEpochSecond())
                .rates(rates.stream().collect(toMap(LatestExchangeRate::getToCurrency, LatestExchangeRate::getRate, (a, b) -> b)))
                .build());
    }

    @Override
    public Optional<ExchangeRates> get(LocalDate date, String base, List<String> symbols) {
        log.info("Fetching exchange rates for {} at {}", base, date);

        ZonedDateTime start = date.atStartOfDay(ZoneOffset.UTC);
        ZonedDateTime end = date.plusDays(1).atStartOfDay(ZoneOffset.UTC);

        List<ExchangeRate> rates = symbols.isEmpty() ?
                exchangeRateRepository.findByFromCurrencyAndCreatedAtIsBetween(base, start, end) :
                exchangeRateRepository.findByFromCurrencyAndCreatedAtIsBetweenAndToCurrencyIn(base, start, end, symbols);

        if (rates.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(ExchangeRates.builder()
                .base(base)
                .timestamp(rates.get(0).getCreatedAt().toEpochSecond())
                .rates(rates.stream().collect(toMap(ExchangeRate::getToCurrency, ExchangeRate::getRate, (a,b) -> b)))
                .build());
    }

    @Override
    @Transactional
    public void save(ExchangeRates rates) {
        log.info("Saving exchange rate {}", rates);
        for (Map.Entry<String, BigDecimal> rate : rates.rates().entrySet()) {
            exchangeRateRepository.save(ExchangeRate.builder()
                .createdAt(ofEpochSecond(rates.timestamp()).atZone(ZoneOffset.UTC))
                .fromCurrency(rates.base())
                .toCurrency(rate.getKey())
                .rate(rate.getValue())
                    .build());
        }
    }
}
