package com.propero.exchangerates.providers;

import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ExchangeRateProvider {

    Optional<ExchangeRates> getLatest(String base, List<String> symbols);

    Optional<ExchangeRates> get(LocalDate date, String base, List<String> symbols);

    default List<ExchangeRates> fetch() {
        throw new UnsupportedOperationException("This provider does not support fetching exchange rates.");
    }

    default void save(ExchangeRates rates) {
        throw new UnsupportedOperationException("This provider does not support saving exchange rates.");
    }

    @Builder
    record ExchangeRates(long timestamp, String base, Map<String, BigDecimal> rates){ }
}
