package com.propero.exchangerates.database;

import org.springframework.data.repository.CrudRepository;

import java.util.Collection;
import java.util.List;

public interface LatestExchangeRateRepository extends CrudRepository<LatestExchangeRate, Long> {

    List<LatestExchangeRate> findByFromCurrency(String fromCurrency);

    List<LatestExchangeRate> findByFromCurrencyAndToCurrencyIn(String fromCurrency, Collection<String> symbols);

}
