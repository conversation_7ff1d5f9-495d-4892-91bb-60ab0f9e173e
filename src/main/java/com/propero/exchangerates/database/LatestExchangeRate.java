package com.propero.exchangerates.database;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Entity
@Getter
@Table(name = "latest_exchange_rates")
public class LatestExchangeRate {
    @Id
    private Long id;
    private ZonedDateTime createdAt;
    private String fromCurrency;
    private String toCurrency;
    private BigDecimal rate;
}
