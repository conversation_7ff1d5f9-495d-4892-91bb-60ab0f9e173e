package com.propero.exchangerates.database;

import org.springframework.data.repository.CrudRepository;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;

public interface ExchangeRateRepository extends CrudRepository<ExchangeRate, Long> {

    List<ExchangeRate> findByFromCurrencyAndCreatedAtIsBetween(String fromCurrency, ZonedDateTime from, ZonedDateTime to);

    List<ExchangeRate> findByFromCurrencyAndCreatedAtIsBetweenAndToCurrencyIn(String fromCurrency, ZonedDateTime from, ZonedDateTime to, Collection<String> symbols);
}
