package com.propero.exchangerates.configuration;

import com.propero.exchangerates.clients.OpenExchangeClient;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import org.springframework.web.util.DefaultUriBuilderFactory;

import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

@Configuration
public class HttpClientConfig {

    @Bean
    public WebClient openExchangeWebClient(OpenExchangeClientProperties properties, WebClient.Builder builder){
        return builder
                .uriBuilderFactory(new DefaultUriBuilderFactory(fromHttpUrl(properties.getUrl()).queryParam("app_id", properties.getApiKey())))
                .build();
    }

    @Bean
    public HttpServiceProxyFactory openExchangeClientProxyFactory(@Qualifier("openExchangeWebClient") WebClient webClient){
        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(webClient)).build();
    }

    @Bean
    public OpenExchangeClient openExchangeClient(@Qualifier("openExchangeClientProxyFactory") HttpServiceProxyFactory httpServiceProxyFactory ){
        return httpServiceProxyFactory.createClient(OpenExchangeClient.class);
    }

    @Getter
    @Setter
    @Component
    @ConfigurationProperties("open-exchange-client")
    public static class OpenExchangeClientProperties {
        private String url;
        private String apiKey;
    }
}
