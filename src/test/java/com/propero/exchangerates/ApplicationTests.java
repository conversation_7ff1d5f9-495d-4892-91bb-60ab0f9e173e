package com.propero.exchangerates;

import com.propero.exchangerates.providers.ExchangeRateProvider.ExchangeRates;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(OutputCaptureExtension.class)
public class ApplicationTests extends AbstractIntegrationTest {

    @Test
    public void applicationStarts() {
        ResponseEntity<AvailabilityResponse> liveness = restTemplate.getForEntity("/actuator/health/liveness", AvailabilityResponse.class);
        assertThat(liveness.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(liveness.getBody()).extracting(AvailabilityResponse::status).isEqualTo("UP");

        ResponseEntity<AvailabilityResponse> readiness = restTemplate.getForEntity("/actuator/health/readiness", AvailabilityResponse.class);
        assertThat(readiness.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(liveness.getBody()).extracting(AvailabilityResponse::status).isEqualTo("UP");
    }

    @Test
    public void headersArePropagatedAndLogged(CapturedOutput output) {
        ResponseEntity<ExchangeRates> response = restTemplate.exchange(RequestEntity.get("/latest?symbols=HEADER_TEST")
//                .header(TRACE_ID_HEADER, "test-trace-id")
                .header(SESSION_ID_HEADER, "test-session-id")
                .header(CUSTOMER_ID_HEADER, "007")
                .build(), ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertNotNull(response.getBody());
        assertThat(response.getBody().rates().get("HEADER_TEST")).isEqualTo(BigDecimal.valueOf(200));
        assertThat(output.getOut()).contains("[test-session-id] [007] [test-trace-id/");
    }

    private record AvailabilityResponse(String status){}
}
