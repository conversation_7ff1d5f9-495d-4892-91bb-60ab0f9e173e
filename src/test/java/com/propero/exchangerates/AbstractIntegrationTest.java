package com.propero.exchangerates;

import com.propero.exchangerates.database.ExchangeRateRepository;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.actuate.observability.AutoConfigureObservability;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

@AutoConfigureObservability
@ActiveProfiles(value = "test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = ExchangeRatesApplication.class)
public class AbstractIntegrationTest {

    public static final String TRACE_ID_HEADER = "traceId";
    public static final String CUSTOMER_ID_HEADER = "customerId";
    public static final String SESSION_ID_HEADER = "sessionId";

    @Autowired
    protected TestRestTemplate restTemplate;

    @Autowired
    private ExchangeRateRepository exchangeRateRepository;

    @BeforeEach
    @Transactional
    public void reset() {
        exchangeRateRepository.deleteAll();
    }

    @DynamicPropertySource
    static void registerProperties(DynamicPropertyRegistry registry) {
        registry.add("DATABASE_HOST", TestContainers.POSTGRES_CONTAINER::getHost);
        registry.add("DATABASE_PORT", () -> TestContainers.POSTGRES_CONTAINER.getMappedPort(5432));
        registry.add("DATABASE_USER", TestContainers.POSTGRES_CONTAINER::getUsername);
        registry.add("DATABASE_PASSWORD", TestContainers.POSTGRES_CONTAINER::getPassword);
        registry.add("DATABASE_NAME", TestContainers.POSTGRES_CONTAINER::getDatabaseName);
        registry.add("OPEN_EXCHANGE_RATES_URL", TestContainers.WIRE_MOCK_CONTAINER::getBaseUrl);
        registry.add("OPEN_EXCHANGE_RATES_KEY", () -> "test-api-key");
    }

    @TestConfiguration
    public static class TestRestTemplateTestConfiguration  {
        @Bean
        public TestRestTemplate testRestTemplate(@LocalServerPort int serverPort, RestTemplateBuilder restTemplateBuilder) {
            return new TestRestTemplate(restTemplateBuilder.rootUri("http://localhost:" + serverPort));
        }
    }
}
