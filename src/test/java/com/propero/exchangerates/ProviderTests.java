package com.propero.exchangerates;

import com.propero.exchangerates.providers.DatabaseExchangeRatesProvider;
import com.propero.exchangerates.providers.ExchangeRateProvider.ExchangeRates;
import org.assertj.core.util.BigDecimalComparator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.math.BigDecimal.valueOf;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.assertj.core.api.InstanceOfAssertFactories.map;

public class ProviderTests extends AbstractIntegrationTest {

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private DatabaseExchangeRatesProvider databaseExchangeRatesProvider;

    @Test
    public void testOpenExchangeRatesLatestApi() {
        ResponseEntity<ExchangeRates> response = restTemplate.getForEntity("/latest", ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody())
            .isNotNull()
            .returns("USD", ExchangeRates::base)
            .extracting(ExchangeRates::rates)
            .asInstanceOf(map(String.class, BigDecimal.class))
            .contains(
                entry("EUR", valueOf(3.672538)),
                entry("GBP", valueOf(0.791234)),
                entry("AUD", valueOf(1.390866))
            );
    }

    @Test
    public void testOpenExchangeRatesHistoricalApi() {
        ResponseEntity<ExchangeRates> response = restTemplate.getForEntity("/historical/2024-05-01", ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody())
            .isNotNull()
            .returns("USD", ExchangeRates::base)
            .extracting(ExchangeRates::rates)
            .asInstanceOf(map(String.class, BigDecimal.class))
            .contains(
                entry("EUR", valueOf(3.672538)),
                entry("GBP", valueOf(0.791234)),
                entry("AUD", valueOf(1.390866))
            );
    }

    @Test
    public void testOpenExchangeRatesWithSpecificSymbol() {
        ResponseEntity<ExchangeRates> response = restTemplate.getForEntity("/latest?symbols=EUR,GBP", ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody())
            .isNotNull()
            .returns("USD", ExchangeRates::base)
            .extracting(ExchangeRates::rates)
            .asInstanceOf(map(String.class, BigDecimal.class))
            .containsExactly(
                entry("EUR", valueOf(3.672538)),
                entry("GBP", valueOf(0.791234))
            );
    }

    @Test
    public void testOpenExchangeRatesNotFound() {
        ResponseEntity<ExchangeRates> response = restTemplate.getForEntity("/historical/2023-01-01", ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
    }

    @Test
    public void testLatestDatabaseExchangeRates() {

        // Save
        transactionTemplate.executeWithoutResult(t -> databaseExchangeRatesProvider.save(ExchangeRates.builder()
                .timestamp(ZonedDateTime.now().toEpochSecond())
                .base("USD")
                .rates(Map.of(
                "EUR", valueOf(1.0),
                "GBP", valueOf(1.1)
                ))
                .build()));

        assertThat(databaseExchangeRatesProvider.getLatest("USD", List.of()))
                .isPresent()
                .map(ExchangeRates::rates)
                .get()
                .asInstanceOf(map(String.class, BigDecimal.class))
                .returns(1.0, m -> m.get("EUR").doubleValue())
                .returns(1.1, m -> m.get("GBP").doubleValue());

        assertThat(databaseExchangeRatesProvider.getLatest("USD", List.of("EUR")))
                .isPresent()
                .map(ExchangeRates::rates)
                .get()
                .asInstanceOf(map(String.class, BigDecimal.class))
                .hasSize(1)
                .returns(1.0, m -> m.get("EUR").doubleValue());
    }

    @Test
    public void testHistoricalDatabaseExchangeRates() {

        OffsetDateTime yesterday = LocalDate.now().minusDays(1).atStartOfDay().atOffset(ZoneOffset.UTC);

        // Save
        transactionTemplate.executeWithoutResult(t -> databaseExchangeRatesProvider.save(ExchangeRates.builder()
                .timestamp(yesterday.toEpochSecond())
                .base("USD")
                .rates(Map.of(
                "EUR", valueOf(1.0),
                "GBP", valueOf(1.1)
                ))
                .build()));

        assertThat(databaseExchangeRatesProvider.get(yesterday.toLocalDate(), "USD", List.of()))
                .isPresent()
                .map(ExchangeRates::rates)
                .get()
                .asInstanceOf(map(String.class, BigDecimal.class))
                .returns(1.0, m -> m.get("EUR").doubleValue())
                .returns(1.1, m -> m.get("GBP").doubleValue());

        assertThat(databaseExchangeRatesProvider.get(LocalDate.now(),"USD", List.of()))
                .isEmpty();
    }

    @Test
    public void testSaveExchangeRatesFromOpenExchangeRates() {
        ResponseEntity<ExchangeRates> response = restTemplate.getForEntity("/latest?symbols=EUR,GBP", ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(databaseExchangeRatesProvider.getLatest( "USD", List.of()))
                .isPresent()
                .map(ExchangeRates::rates)
                .get()
                .asInstanceOf(map(String.class, BigDecimal.class))
                .hasSize(2)
                .returns(3.672538, m -> m.get("EUR").doubleValue())
                .returns(0.791234, m -> m.get("GBP").doubleValue());
    }

    @Test
    public void testRefresh() {
        ResponseEntity<ExchangeRates> response = restTemplate.getForEntity("/refresh", ExchangeRates.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);

        assertThat(databaseExchangeRatesProvider.getLatest( "USD", List.of()))
                .isPresent()
                .map(ExchangeRates::rates)
                .get()
                .asInstanceOf(map(String.class, BigDecimal.class))
                .hasSize(3)
                .returns(3.672538, m -> m.get("EUR").doubleValue())
                .returns(0.791234, m -> m.get("GBP").doubleValue())
                .returns(1.390866, m -> m.get("AUD").doubleValue());;

        assertThat(databaseExchangeRatesProvider.getLatest( "EUR", List.of()))
                .isPresent()
                .map(ExchangeRates::rates)
                .get()
                .asInstanceOf(map(String.class, BigDecimal.class))
                .hasSize(2)
                .returns(1.091232, m -> m.get("USD").doubleValue())
                .returns(0.855442, m -> m.get("GBP").doubleValue());
    }

    @ParameterizedTest
    @ValueSource(doubles = {
        **********1234.0,
        0.12345678912345,
        **********.**********
    })
    public void testExchangeRatePrecision(double values) {
        BigDecimal value = valueOf(values);
        transactionTemplate.executeWithoutResult(t ->databaseExchangeRatesProvider.save(ExchangeRates.builder()
                .base("FOO")
                .timestamp(ZonedDateTime.now().toEpochSecond())
                .rates(Map.of("BAR", value))
                .build()));

        Optional<BigDecimal> storedValue = databaseExchangeRatesProvider.getLatest("FOO", List.of("BAR")).map(r -> r.rates().get("BAR"));
        assertThat(storedValue)
                .isPresent()
                .get()
                .usingComparator(BigDecimalComparator.BIG_DECIMAL_COMPARATOR)
                .isEqualTo(value);

    }
}
