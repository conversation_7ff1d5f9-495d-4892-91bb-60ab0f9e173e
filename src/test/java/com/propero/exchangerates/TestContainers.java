package com.propero.exchangerates;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.testcontainers.containers.BindMode;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.wiremock.integrations.testcontainers.WireMockContainer;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

import static org.testcontainers.utility.DockerImageName.parse;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TestContainers {

    private static final int DEFAULT_CONTAINER_TIMEOUT = 30;

    public static final PostgreSQLContainer<?> POSTGRES_CONTAINER = new PostgreSQLContainer<>(parse("postgres:11.5"))
            .withLogConsumer(new Slf4jLogConsumer(log))
            .waitingFor(Wait.forSuccessfulCommand("pg_isready -d exchange_rates_db"))
            .withStartupTimeout(Duration.of(DEFAULT_CONTAINER_TIMEOUT, ChronoUnit.SECONDS))
            .withDatabaseName("exchange_rates_db")
            .withUsername("exchangerates")
            .withPassword("exchangerates")
            .withNetworkAliases("postgres");

    public static final WireMockContainer WIRE_MOCK_CONTAINER = new WireMockContainer(parse(WireMockContainer.OFFICIAL_IMAGE_NAME).withTag("3.6.0"))
            .withLogConsumer(new Slf4jLogConsumer(log))
            .withStartupTimeout(Duration.of(DEFAULT_CONTAINER_TIMEOUT, ChronoUnit.SECONDS))
            .withClasspathResourceMapping("wiremock", "/home/<USER>/", BindMode.READ_ONLY);

    static {
        POSTGRES_CONTAINER.start();
        WIRE_MOCK_CONTAINER.start();
    }
}
