{"mappings": [{"priority": 1, "request": {"method": "GET", "urlPath": "/api/latest.json", "queryParameters": {"app_id": {"equalTo": "test-api-key"}, "base": {"equalTo": "USD"}, "symbols": {"equalTo": "EUR,GBP"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"disclaimer": "https://openexchangerates.org/terms/", "license": "https://openexchangerates.org/license/", "timestamp": "{{now format='unix'}}", "base": "USD", "rates": {"EUR": 3.672538, "GBP": 0.791234}}, "transformers": ["response-template"]}}, {"priority": 2, "request": {"method": "GET", "urlPath": "/api/latest.json", "headers": {"sessionid": {"equalTo": "test-session-id"}, "customerid": {"equalTo": "007"}}, "queryParameters": {"symbols": {"equalTo": "HEADER_TEST"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"disclaimer": "https://openexchangerates.org/terms/", "license": "https://openexchangerates.org/license/", "timestamp": "{{now format='unix'}}", "base": "USD", "rates": {"HEADER_TEST": 200}}, "transformers": ["response-template"]}}, {"priority": 100, "request": {"method": "GET", "urlPath": "/api/latest.json", "queryParameters": {"app_id": {"equalTo": "test-api-key"}, "base": {"equalTo": "USD"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"disclaimer": "https://openexchangerates.org/terms/", "license": "https://openexchangerates.org/license/", "timestamp": "{{now format='unix'}}", "base": "USD", "rates": {"EUR": 3.672538, "GBP": 0.791234, "AUD": 1.390866}}, "transformers": ["response-template"]}}, {"request": {"method": "GET", "urlPathTemplate": "/api/historical/{date}.json", "pathParameters": {"date": {"equalTo": "2024-05-01"}}, "queryParameters": {"app_id": {"equalTo": "test-api-key"}, "base": {"equalTo": "USD"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"disclaimer": "https://openexchangerates.org/terms/", "license": "https://openexchangerates.org/license/", "timestamp": **********, "base": "USD", "rates": {"EUR": 3.672538, "GBP": 0.791234, "AUD": 1.390866}}, "transformers": ["response-template"]}}, {"request": {"method": "GET", "urlPath": "/api/latest.json", "queryParameters": {"app_id": {"equalTo": "test-api-key"}, "base": {"equalTo": "EUR"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"disclaimer": "https://openexchangerates.org/terms/", "license": "https://openexchangerates.org/license/", "timestamp": "{{now format='unix'}}", "base": "EUR", "rates": {"USD": 1.091232, "GBP": 0.855442}}, "transformers": ["response-template"]}}]}