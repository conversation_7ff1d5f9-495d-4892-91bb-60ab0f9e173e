plugins {
	id 'java'
	id 'jacoco'
	id 'org.springframework.boot' version '3.2.1'
	id 'io.spring.dependency-management' version '1.1.4'
}

apply from: 'test.gradle'

group = 'com.propero'
version = System.getenv('VERSION') ?: 'local'

java {
	sourceCompatibility = JavaVersion.VERSION_17
	targetCompatibility = JavaVersion.VERSION_17
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
	mavenLocal()
}

dependencies {
	implementation group: 'org.springframework.boot', name: 'spring-boot-starter-actuator'
	implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-jpa'
	implementation group: 'org.springframework.boot', name: 'spring-boot-starter-web'
	implementation group: 'org.springframework.boot', name: 'spring-boot-starter-webflux'

	implementation group: 'io.micrometer', 			name: 'micrometer-tracing'
	implementation group: 'io.micrometer', 			name: 'micrometer-tracing-bridge-otel'
	implementation group: 'io.micrometer', 			name: 'micrometer-registry-otlp'

	implementation group: 'ch.qos.logback', 		name: 'logback-core'
	implementation group: 'ch.qos.logback', 		name: 'logback-classic'
	implementation group: 'ch.qos.logback', 		name: 'logback-access'
	implementation group: 'net.logstash.logback', 	name: 'logstash-logback-encoder', 			version: '7.4'

	runtimeOnly group: 'org.liquibase', 			name: 'liquibase-core'
	implementation group: 'org.postgresql', 		name: 'postgresql'

	implementation group: 'org.springdoc', 			name: 'springdoc-openapi-starter-webmvc-ui', version: '2.5.0'

	compileOnly group: 'org.projectlombok',				   name: 'lombok'
	annotationProcessor group: 'org.projectlombok', 	   name: 'lombok'
	annotationProcessor group: 'org.springframework.boot', name: 'spring-boot-configuration-processor'

	developmentOnly group: 'org.springframework.boot', 	   name: 'spring-boot-devtools'
}

bootJar {
	archiveFileName = 'exchange-rate-service.jar'
}

defaultTasks 'clean', 'classes', 'testClasses', 'check', 'assemble'