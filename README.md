# Exchange Rates service

This service is will keep track of exchange rates by periodically fetching them from openexchangerates.org.

### Running the service locally

Start by create a new `.env` file in the root of the project.

Add the `OPEN_EXCHANGE_RATES_KEY=...` to the file. This key will allow the service to fetch exchange rates.

Run the service by either running  `java/com/propero/exchangerates/ExchangeRatesApplication.java` (Active profile local)  
in your preferred IDE  or use Gradle directly by running `./gradlew bootRun --args='--spring.profiles.active=local'`.

### Swagger available

End-point information in Swagger http://service-url/swagger-ui.html



